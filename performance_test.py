#!/usr/bin/env python3
"""
Performance testing script for Resume Quick Finder optimizations.
Tests the speed improvements in folder processing and database operations.
"""

import os
import sys
import time
import random
import string
import tempfile
import shutil
from pathlib import Path

# Add the current directory to the path so we can import from resume_quick_finder
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_files(num_files, test_dir):
    """Create test PDF and DOCX files for performance testing."""
    print(f"Creating {num_files} test files in {test_dir}...")
    
    # Sample resume content templates
    resume_templates = [
        """
        John Doe
        Software Engineer
        Email: <EMAIL>
        Phone: +61412345678
        Location: Sydney, NSW
        
        PROFESSIONAL SUMMARY
        Experienced software developer with 5+ years in Python, Java, and cloud technologies.
        Strong background in agile development and team leadership.
        
        EXPERIENCE
        Senior Software Engineer - Tech Corp (2020-Present)
        - Developed microservices using Python and Docker
        - Led team of 4 developers
        - Implemented CI/CD pipelines using Jenkins
        
        Software Developer - StartupXYZ (2018-2020)
        - Built web applications using React and Node.js
        - Worked with AWS services including EC2, S3, and RDS
        - Participated in agile development processes
        
        EDUCATION
        Bachelor of Computer Science
        University of Technology Sydney (2014-2018)
        
        SKILLS
        Programming: Python, Java, JavaScript, SQL
        Cloud: AWS, Docker, Kubernetes
        Frameworks: React, Spring Boot, Django
        """,
        
        """
        Jane Smith
        Project Manager
        Email: <EMAIL>
        Phone: +61423456789
        Location: Melbourne, VIC
        
        PROFESSIONAL SUMMARY
        Certified Project Manager with 8+ years experience managing complex IT projects.
        PMP certified with expertise in Agile and Waterfall methodologies.
        
        EXPERIENCE
        Senior Project Manager - Enterprise Solutions (2019-Present)
        - Managed projects worth $2M+ with teams of 15+ members
        - Delivered 95% of projects on time and within budget
        - Implemented Agile transformation across organization
        
        Project Coordinator - Business Systems (2016-2019)
        - Coordinated multiple concurrent projects
        - Facilitated stakeholder meetings and requirements gathering
        - Managed project documentation and reporting
        
        EDUCATION
        Master of Business Administration
        Melbourne Business School (2014-2016)
        
        Bachelor of Information Technology
        RMIT University (2010-2014)
        
        CERTIFICATIONS
        - Project Management Professional (PMP)
        - Certified Scrum Master (CSM)
        - PRINCE2 Foundation
        
        SKILLS
        Project Management: Agile, Scrum, Waterfall, PRINCE2
        Tools: Jira, Confluence, MS Project, Trello
        """,
        
        """
        Michael Johnson
        Data Analyst
        Email: <EMAIL>
        Phone: +***********
        Location: Brisbane, QLD
        
        PROFESSIONAL SUMMARY
        Data-driven analyst with 4+ years experience in business intelligence and analytics.
        Expert in SQL, Python, and data visualization tools.
        
        EXPERIENCE
        Senior Data Analyst - Analytics Corp (2021-Present)
        - Developed automated reporting dashboards using Tableau
        - Performed statistical analysis on large datasets
        - Collaborated with business stakeholders to define KPIs
        
        Data Analyst - Retail Chain (2019-2021)
        - Created ETL pipelines using Python and SQL
        - Built predictive models for sales forecasting
        - Reduced reporting time by 60% through automation
        
        EDUCATION
        Bachelor of Statistics
        Queensland University of Technology (2015-2019)
        
        SKILLS
        Programming: Python, R, SQL
        Visualization: Tableau, Power BI, matplotlib
        Databases: PostgreSQL, MySQL, MongoDB
        Statistics: Regression, Time Series, Machine Learning
        """
    ]
    
    created_files = []
    
    for i in range(num_files):
        # Randomly select a template and customize it
        template = random.choice(resume_templates)
        
        # Customize the template with unique data
        name_variations = [
            f"Test User {i:04d}",
            f"Sample Candidate {i:04d}",
            f"Demo Person {i:04d}"
        ]
        
        customized_content = template.replace("John Doe", random.choice(name_variations))
        customized_content = customized_content.replace("<EMAIL>", f"user{i:04d}@example.com")
        customized_content = customized_content.replace("+61412345678", f"+614{random.randint(10000000, 99999999)}")
        
        # Add some random content to make files more realistic
        additional_content = f"""
        
        ADDITIONAL EXPERIENCE
        Previous Role {i} - Company {i % 10}
        - Responsibility {random.randint(1, 100)}
        - Achievement {random.randint(1, 100)}
        
        PROJECTS
        Project Alpha-{i}: {' '.join(random.choices(['Web', 'Mobile', 'Desktop', 'Cloud', 'AI', 'ML'], k=3))} application
        Project Beta-{i}: {' '.join(random.choices(['Database', 'API', 'Frontend', 'Backend', 'DevOps'], k=2))} solution
        """
        
        full_content = customized_content + additional_content
        
        # Create both PDF and DOCX files (simulate with text files for testing)
        if i % 2 == 0:
            # Create a "PDF" (text file with .pdf extension for testing)
            file_path = os.path.join(test_dir, f"resume_{i:04d}.pdf")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(full_content)
        else:
            # Create a "DOCX" (text file with .docx extension for testing)
            file_path = os.path.join(test_dir, f"resume_{i:04d}.docx")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(full_content)
        
        created_files.append(file_path)
        
        if (i + 1) % 100 == 0:
            print(f"Created {i + 1} files...")
    
    print(f"Created {len(created_files)} test files")
    return created_files

def test_processing_performance(file_paths, test_name):
    """Test the processing performance with the given file paths."""
    print(f"\n=== {test_name} ===")
    print(f"Testing with {len(file_paths)} files")
    
    try:
        # Import after creating test files to avoid import issues
        from resume_quick_finder import BasicResumeProcessor, DatabaseManager
        
        # Create a temporary database for testing
        test_db_path = f"test_performance_{int(time.time())}.db"
        db_manager = DatabaseManager(test_db_path)
        
        # Test basic processing (no AI)
        print("Starting basic processing test...")
        start_time = time.time()
        
        processor = BasicResumeProcessor(file_paths, db_manager, auto_convert_docx_to_pdf=False)
        
        # Connect signals for monitoring
        results = []
        errors = []
        
        def on_complete(result_list):
            results.extend(result_list)
        
        def on_error(error_msg):
            errors.append(error_msg)
        
        def on_progress(progress):
            if progress % 20 == 0:  # Log every 20%
                print(f"Progress: {progress}%")
        
        processor.complete_signal.connect(on_complete)
        processor.error_signal.connect(on_error)
        processor.progress_signal.connect(on_progress)
        
        # Run the processor
        processor.start()
        processor.wait()  # Wait for completion
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n{test_name} Results:")
        print(f"  Total files: {len(file_paths)}")
        print(f"  Processing time: {processing_time:.2f} seconds")
        print(f"  Files per second: {len(file_paths) / processing_time:.2f}")
        print(f"  Successfully processed: {len(results)}")
        print(f"  Errors: {len(errors)}")
        
        if errors:
            print(f"  Sample errors: {errors[:3]}")
        
        # Clean up test database
        try:
            os.remove(test_db_path)
        except:
            pass
        
        return {
            'test_name': test_name,
            'total_files': len(file_paths),
            'processing_time': processing_time,
            'files_per_second': len(file_paths) / processing_time,
            'successful': len(results),
            'errors': len(errors)
        }
        
    except Exception as e:
        print(f"Error during {test_name}: {e}")
        return None

def main():
    """Main performance testing function."""
    print("🚀 Resume Quick Finder Performance Test Suite")
    print("=" * 60)
    
    # Create temporary directory for test files
    test_dir = tempfile.mkdtemp(prefix="resume_perf_test_")
    print(f"Test directory: {test_dir}")
    
    try:
        # Test different file counts to measure scalability
        test_scenarios = [
            ("Small Dataset", 50),
            ("Medium Dataset", 200),
            ("Large Dataset", 500),
        ]
        
        results = []
        
        for scenario_name, file_count in test_scenarios:
            print(f"\n{'='*20} {scenario_name} ({'='*20}")
            
            # Create test files for this scenario
            test_files = create_test_files(file_count, test_dir)
            
            # Test processing performance
            result = test_processing_performance(test_files, f"{scenario_name} Processing")
            if result:
                results.append(result)
            
            # Clean up files for this scenario
            for file_path in test_files:
                try:
                    os.remove(file_path)
                except:
                    pass
        
        # Print summary
        print(f"\n{'='*20} PERFORMANCE SUMMARY {'='*20}")
        print(f"{'Test Name':<25} {'Files':<8} {'Time(s)':<10} {'Files/sec':<12} {'Success':<8}")
        print("-" * 70)
        
        for result in results:
            print(f"{result['test_name']:<25} {result['total_files']:<8} "
                  f"{result['processing_time']:<10.2f} {result['files_per_second']:<12.2f} "
                  f"{result['successful']:<8}")
        
        # Performance analysis
        if len(results) >= 2:
            print(f"\n📊 Performance Analysis:")
            small_fps = results[0]['files_per_second']
            large_fps = results[-1]['files_per_second']
            
            if large_fps >= small_fps * 0.8:  # Within 20% of small dataset performance
                print("✅ Good scalability: Performance maintained with larger datasets")
            elif large_fps >= small_fps * 0.5:  # Within 50%
                print("⚠️  Moderate scalability: Some performance degradation with larger datasets")
            else:
                print("❌ Poor scalability: Significant performance degradation with larger datasets")
            
            print(f"   Small dataset: {small_fps:.2f} files/sec")
            print(f"   Large dataset: {large_fps:.2f} files/sec")
            print(f"   Efficiency ratio: {(large_fps/small_fps)*100:.1f}%")
        
        print(f"\n🎯 Optimization Recommendations:")
        avg_fps = sum(r['files_per_second'] for r in results) / len(results)
        
        if avg_fps > 10:
            print("✅ Excellent performance: No immediate optimizations needed")
        elif avg_fps > 5:
            print("✅ Good performance: Consider optimizations for very large datasets")
        elif avg_fps > 2:
            print("⚠️  Moderate performance: Optimizations recommended")
        else:
            print("❌ Poor performance: Significant optimizations needed")
        
        print(f"   Average processing speed: {avg_fps:.2f} files/second")
        
    finally:
        # Clean up test directory
        try:
            shutil.rmtree(test_dir)
            print(f"\nCleaned up test directory: {test_dir}")
        except Exception as e:
            print(f"Warning: Could not clean up test directory {test_dir}: {e}")

if __name__ == "__main__":
    main()
