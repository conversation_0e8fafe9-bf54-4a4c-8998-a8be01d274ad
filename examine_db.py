#!/usr/bin/env python3
import sqlite3
import os

def examine_database():
    db_path = 'resume_database.db'
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return
    
    # Get file size
    file_size = os.path.getsize(db_path)
    print(f"Database file size: {file_size:,} bytes ({file_size / (1024*1024):.2f} MB)")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get table info
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"\nTables: {[t[0] for t in tables]}")
        
        # Get count of records
        cursor.execute("SELECT COUNT(*) FROM resumes")
        resume_count = cursor.fetchone()[0]
        print(f"Number of resumes: {resume_count:,}")
        
        cursor.execute("SELECT COUNT(*) FROM resume_data")
        resume_data_count = cursor.fetchone()[0]
        print(f"Number of resume_data records: {resume_data_count:,}")
        
        # Check existing indexes
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
        indexes = cursor.fetchall()
        print(f"\nCurrent indexes ({len(indexes)}):")
        for idx in indexes:
            print(f"  {idx[0]}: {idx[1]}")
        
        # Check table schemas
        print("\nTable schemas:")
        for table_name in ['resumes', 'resume_data']:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"\n{table_name} table:")
            for col in columns:
                print(f"  {col[1]} {col[2]} {'PRIMARY KEY' if col[5] else ''} {'NOT NULL' if col[3] else ''}")
        
        # Sample some data sizes
        cursor.execute("SELECT AVG(LENGTH(raw_text)), MAX(LENGTH(raw_text)), MIN(LENGTH(raw_text)) FROM resume_data WHERE raw_text IS NOT NULL")
        text_stats = cursor.fetchone()
        if text_stats[0]:
            print(f"\nRaw text statistics:")
            print(f"  Average length: {text_stats[0]:.0f} characters")
            print(f"  Maximum length: {text_stats[1]:,} characters")
            print(f"  Minimum length: {text_stats[2]:,} characters")
        
        # Check for any performance issues
        cursor.execute("ANALYZE")
        
    except Exception as e:
        print(f"Error examining database: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    examine_database()
