#!/usr/bin/env python3
"""
Test script for database optimization features.
This script tests the new database optimization functionality without requiring the full GUI.
"""

import sys
import os
import sqlite3
import time
import random
import string

# Add the current directory to the path so we can import from resume_quick_finder
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_database(db_path, num_records=1000):
    """Create a test database with sample data."""
    print(f"Creating test database with {num_records} records...")
    
    # Remove existing database
    if os.path.exists(db_path):
        os.remove(db_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create tables (simplified version)
    cursor.execute('''
    CREATE TABLE resumes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        filename TEXT NOT NULL,
        file_path TEXT NOT NULL,
        upload_date TEXT NOT NULL,
        content_hash TEXT UNIQUE
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE resume_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resume_id INTEGER NOT NULL,
        full_name TEXT,
        email TEXT,
        phone TEXT,
        location TEXT,
        security_clearance TEXT,
        attributes TEXT,
        objectives TEXT,
        skills TEXT,
        experience TEXT,
        education TEXT,
        certifications TEXT,
        languages TEXT,
        projects TEXT,
        raw_text TEXT,
        tags TEXT,
        FOREIGN KEY (resume_id) REFERENCES resumes (id)
    )
    ''')
    
    # Generate sample data
    locations = ["Sydney, NSW", "Melbourne, VIC", "Brisbane, QLD", "Perth, WA", "Adelaide, SA"]
    clearances = ["Baseline", "NV1", "NV2", "TSPV", ""]
    skills = ["Python", "Java", "JavaScript", "C++", "SQL", "AWS", "Azure", "Docker", "Kubernetes"]
    
    for i in range(num_records):
        # Insert resume record
        filename = f"resume_{i:06d}.pdf"
        file_path = f"/path/to/{filename}"
        upload_date = "2024-01-01 12:00:00"
        content_hash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
        
        cursor.execute('''
        INSERT INTO resumes (filename, file_path, upload_date, content_hash)
        VALUES (?, ?, ?, ?)
        ''', (filename, file_path, upload_date, content_hash))
        
        resume_id = cursor.lastrowid
        
        # Insert resume_data record
        full_name = f"Test User {i:06d}"
        email = f"user{i:06d}@example.com"
        phone = f"+61{random.randint(400000000, 499999999)}"
        location = random.choice(locations)
        security_clearance = random.choice(clearances)
        
        # Generate large raw text to simulate real resumes
        raw_text = f"""
        {full_name}
        Email: {email}
        Phone: {phone}
        Location: {location}
        
        PROFESSIONAL SUMMARY
        Experienced software developer with {random.randint(1, 15)} years of experience in various technologies.
        Skilled in {', '.join(random.sample(skills, 3))}.
        
        EXPERIENCE
        Senior Developer at Tech Company ({2020 + random.randint(0, 4)} - Present)
        - Developed applications using {random.choice(skills)}
        - Led team of {random.randint(2, 8)} developers
        - Implemented CI/CD pipelines
        
        EDUCATION
        Bachelor of Computer Science
        University of Technology
        
        SKILLS
        Technical: {', '.join(random.sample(skills, 5))}
        """ * 10  # Make it longer to simulate real resume content
        
        tags = f'["{random.choice(skills)}", "{random.choice(locations.split(", ")[0])}", "Developer"]'
        
        cursor.execute('''
        INSERT INTO resume_data (
            resume_id, full_name, email, phone, location, security_clearance,
            attributes, objectives, skills, experience, education, certifications,
            languages, projects, raw_text, tags
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            resume_id, full_name, email, phone, location, security_clearance,
            "Experienced, Reliable", "Seeking senior role", '{}', '[]', '[]', '[]',
            '{}', '[]', raw_text, tags
        ))
        
        if (i + 1) % 100 == 0:
            print(f"Created {i + 1} records...")
    
    conn.commit()
    conn.close()
    print(f"Test database created with {num_records} records")

def test_search_performance(db_path):
    """Test search performance before and after optimization."""
    try:
        # Import the DatabaseManager from our main file
        from resume_quick_finder import DatabaseManager
        
        db_manager = DatabaseManager(db_path)
        
        print("\n=== Testing Search Performance ===")
        
        # Test queries
        test_queries = [
            "Python",
            "Sydney",
            "Senior Developer",
            "Python AND Sydney",
            "Developer OR Engineer"
        ]
        
        for query in test_queries:
            print(f"\nTesting query: '{query}'")
            
            # Time the search
            start_time = time.time()
            results, terms = db_manager.search_resumes_boolean(query)
            end_time = time.time()
            
            print(f"  Results: {len(results)} found")
            print(f"  Time: {(end_time - start_time)*1000:.2f} ms")
        
        return True
        
    except ImportError as e:
        print(f"Could not import DatabaseManager: {e}")
        return False
    except Exception as e:
        print(f"Error testing search performance: {e}")
        return False

def test_optimization(db_path):
    """Test database optimization features."""
    try:
        from resume_quick_finder import DatabaseManager
        
        db_manager = DatabaseManager(db_path)
        
        print("\n=== Testing Database Optimization ===")
        
        # Get initial stats
        print("Initial database statistics:")
        stats = db_manager.get_database_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Run optimization
        print("\nRunning database optimization...")
        success, message = db_manager.optimize_database()
        
        if success:
            print(f"✅ Optimization successful: {message}")
        else:
            print(f"❌ Optimization failed: {message}")
            return False
        
        # Get post-optimization stats
        print("\nPost-optimization database statistics:")
        stats = db_manager.get_database_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"Error testing optimization: {e}")
        return False

def main():
    """Main test function."""
    test_db_path = "test_resume_database.db"
    
    print("🧪 Database Optimization Test Suite")
    print("=" * 50)
    
    # Create test database
    num_records = 5000  # Start with 5k records for testing
    create_test_database(test_db_path, num_records)
    
    # Test search performance before optimization
    print("\n📊 Testing search performance BEFORE optimization:")
    test_search_performance(test_db_path)
    
    # Test optimization
    test_optimization(test_db_path)
    
    # Test search performance after optimization
    print("\n📊 Testing search performance AFTER optimization:")
    test_search_performance(test_db_path)
    
    print(f"\n✅ Test completed. Test database saved as: {test_db_path}")
    print("You can now test the optimized database with the main application.")

if __name__ == "__main__":
    main()
